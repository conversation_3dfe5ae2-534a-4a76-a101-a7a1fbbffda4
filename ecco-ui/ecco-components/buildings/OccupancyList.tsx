import * as React from "react";
import {useState, useEffect} from "react";
import {
    CircularProgress,
    Box
} from "@eccosolutions/ecco-mui";
import {
    OccupancyHistoryDto
} from "ecco-dto";
import {useServicesContext} from "../ServicesContext";
import {EccoDate} from "@eccosolutions/ecco-common";

/**
 * Component for displaying occupancy lists
 */
export const OccupancyList: React.FC = () => {
    const {getBuildingRepository} = useServicesContext();

    const [occupancyHistory, setOccupancyHistory] = useState<OccupancyHistoryDto[]>([]);
    const [loading, setLoading] = useState<boolean>(true);
    const [pageNumber, setPageNumber] = useState<number>(0);
    const [from, setFrom] = useState<EccoDate>(EccoDate.todayLocalTime());
    const [to, setTo] = useState<EccoDate>(EccoDate.todayLocalTime());
    const [error, setError] = useState<string | null>(null);
    // TODO possibly hierarchy buildingIds?
    const [buildingIds, setBuildingIds] = useState<number[] | undefined>(undefined);

    useEffect(() => {
        loadOccupancy();
    }, [from, to, pageNumber, buildingIds]);

    const loadOccupancy = () => {
        setLoading(true);
        getBuildingRepository()
            .findOccupancyHistory(from, to, pageNumber, buildingIds)
            .then(response => {
                setOccupancyHistory(response);
                setError(null);
                setLoading(false);
            })
            .catch(err => {
                console.error("failed to load occupancy history", err);
                setError("failed to load occupancy history");
                setLoading(false);
            });
        /*.finally(() => {
                setLoading(false);
            });*/
    };

    if (loading && occupancyHistory.length === 0) {
        return (
            <Box display="flex" justifyContent="center" p={2}>
                <CircularProgress />
            </Box>
        );
    }

    // TODO return a building
    return <></>;
};
